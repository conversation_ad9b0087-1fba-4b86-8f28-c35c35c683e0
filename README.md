# 自动化生图工具

这是一个基于Flask和现代Web技术构建的自动化图片生成工具，支持批量处理Excel文件中的分镜数据，调用OpenAI兼容的图片生成API来自动生成图片。

## ✨ 主要功能

### 1. 通用模型配置
- 支持添加多个OpenAI兼容的模型服务
- 自定义API密钥、接口地址、超时时间（默认3分钟）、重试次数（默认3次）
- 支持对话模型和生图模型分类管理，自动选择第一个可用的生图模型

### 2. 角色配置
- 配置多个角色，每个角色包含唯一编码、角色名、角色URL
- **角色核心特征**: 支持为角色配置不可变的核心特征描述
- 角色图片预览功能，点击可放大查看
- 在生图时可选择最多5个角色作为参考图，自动生成包含核心特征的参考图说明

### 3. GitHub图床功能
- **GitHub集成**: 支持上传图片到GitHub仓库作为图床
- **拖拽上传**: 支持拖拽文件到上传区域，自动按时间戳命名
- **即时预览**: 上传成功后立即显示图片和链接，一键复制到剪贴板

### 4. 角色形象功能
- **多提示词管理**: 支持添加多个角色生成提示词
- **并发生图**: 支持多个角色同时生成，提高效率
- **智能命名**: 生成的图片按时间戳自动命名，MD5去重避免重复
- **完整预览**: 生成完成后立即显示，支持图片导航和模态框预览

### 5. 批量抽图功能
- **Excel文件导入**: 读取包含"镜头序号"和"文生图prompt"列的Excel文件
- **分镜管理**: 每个分镜可以单独配置、启用/禁用
- **参考角色选择**: 为每个分镜选择参考角色，自动生成参考图说明
- **批量生图**: 一键生成所有启用的分镜，支持真正的并发处理
- **Excel导出**: 导出包含分镜数据、提示词、参考图说明和角色URL的Excel文件

### 6. 图片管理功能
- **图片预览**: 生成的图片自动显示在分镜卡片中，9:16比例缩略图
- **图片导航**: 支持上一张/下一张图片浏览，点击图片可在模态框中放大查看
- **智能布局**: 图片横向排列，每行最多5张，自动换行

### 7. 数据持久化系统
- 所有配置自动保存到本地JSON文件和浏览器本地存储
- 页面刷新后自动恢复数据状态，提供手动保存按钮确保数据安全

### 8. Excel编辑器
- **在线编辑**: 支持上传和在线编辑Excel文件（.xlsx/.xls格式）
- **智能列识别**: 自动识别序号列、提示词列、链接列等，设置合适的列宽
- **实时编辑**: 点击单元格即可编辑，支持拖拽调整列宽和行高
- **导出功能**: 编辑完成后可导出包含所有修改的Excel文件

### 9. 进度跟踪与日志
- 实时显示批量生成的进度条和预估完成时间
- 完整的操作日志记录，支持按TraceID跟踪并发操作

## 🏗️ 技术架构

### 后端技术栈
- **Flask**: Web框架，**Python 3.8+**: 核心语言
- **pandas + openpyxl**: Excel文件处理，**requests**: HTTP客户端
- **模块化设计**: config_manager(配置管理)、model_service(模型服务调用)、character_manager(角色管理)、excel_processor(Excel处理)、image_generator(图片生成)、logger(日志系统)

### 前端技术栈
- **原生JavaScript**: 无框架依赖，性能优异
- **CSS Grid/Flexbox**: 现代布局，响应式设计
- **LocalStorage**: 数据持久化，模态框图片预览

## 🚀 性能优化

### 并发处理优化
- **真正的并发**: 移除了分镜级别的并发限制（原来最多3个分镜同时处理）
- **无限制并发**: 单个分镜内图片生成不再限制为5个，而是根据实际需求并发
- **效果**: 18个分镜×2张图片 = 36个API调用同时发起，大大缩短总体生成时间
- **注意**: 高并发会对API服务器造成压力，请确保服务器能够承受

### 界面布局优化
- **9:16比例缩略图**: 所有图片缩略图改为9:16比例，匹配实际生成图片
- **智能Grid布局**: 使用CSS Grid替代Flex布局，图片横向排列，每行最多5张
- **响应式尺寸**: 桌面端100×178px，移动端70×124px，自动适配不同屏幕

### 文件命名优化
- **毫秒时间戳**: 使用毫秒级时间戳避免文件重名覆盖
- **格式**: `excel文件名_分镜X_毫秒时间戳.png`
- **唯一性**: 即使在高并发情况下也基本不会重名

## 🚀 快速开始

### 系统要求
- Windows 10/11，Python 3.8+，4GB+ 内存

### 一键启动
双击运行 `start.bat` 文件，脚本会自动检查Python环境、创建虚拟环境、安装依赖包、启动Web服务器

### 手动安装
```bash
python -m venv venv && venv\Scripts\activate && pip install -r requirements.txt && python app.py
```

### 访问应用
打开浏览器访问: http://localhost:5000

## 📖 使用流程

### 1. 配置模型服务
点击"模型配置"标签页，添加OpenAI兼容服务（服务名称、API地址、API密钥、超时时间、重试次数）

### 2. 配置角色参考图
点击"角色配置"标签页，添加角色（角色名称、角色编码、角色URL、角色核心特征）

### 3. 配置GitHub图床（可选）
点击"图床"标签页，配置GitHub Token和仓库路径，支持拖拽上传图片

### 4. 准备Excel文件
创建包含"镜头序号"和"文生图prompt"列的Excel文件，或使用Excel编辑器在线编辑

### 5. 生成图片
1. 在"批量抽图"页面选择Excel文件，或在"Excel编辑"页面编辑后导出
2. 选择模型服务和生图模型（自动选择第一个可用）
3. 设置保存目录，为每个分镜选择参考角色（可选）
4. 调整生图数量，点击"一键生成所有分镜"

### 6. 管理生成的图片
生成完成后，图片会显示在对应分镜卡片中，支持导航浏览、放大查看、导出Excel文件

## 🔧 核心特性

### 角色核心特征系统
为角色配置不可变的核心特征描述，自动在参考图说明中添加核心特征，确保生成图片中角色特征的一致性

### 数据持久化系统
关键操作后自动保存数据到浏览器本地存储，提供"保存缓存"按钮主动保存，页面刷新后自动恢复所有数据状态

### 并发处理系统
- **真正的并发**: 移除分镜级别和单个分镜内的并发限制
- **高效生成**: 18个分镜×2张图片 = 36个API调用同时发起
- **注意事项**: 高并发会对API服务器造成压力，请确保服务器能够承受

### 智能布局系统
- **9:16比例缩略图**: 匹配实际生成图片比例
- **Grid布局**: 图片横向排列，每行最多5张，自动换行
- **响应式设计**: 桌面端100×178px，移动端70×124px

## ⚙️ 配置文件

### config.json 主要字段
```json
{
  "model_services": [{"id": 1, "name": "OpenAI GPT-4", "api_url": "...", "api_key": "...", "timeout": 180, "retry_count": 3, "models": [{"name": "dall-e-3", "type": "image"}]}],
  "characters": [{"id": "1", "name": "角色名", "code": "角色编码", "url": "图片URL", "core_features": "核心特征描述"}],
  "imagehost": {"github_token": "your-token", "repo_path": "/user/repo/contents/path", "max_file_size": 3},
  "settings": {"save_directory": "保存目录", "timeout": 180, "retry_count": 3, "default_service": "1"}
}
```

## 🔧 故障排除

### 常见问题
1. **无法启动**: 检查Python版本3.8+，依赖安装，端口5000占用
2. **Excel读取失败**: 确保文件格式.xlsx/.xls，包含"镜头序号"和"文生图prompt"列
3. **API调用失败**: 检查网络连接、API密钥、地址格式、模型服务状态
4. **图片保存失败**: 检查保存目录权限、磁盘空间、图片URL可访问性
5. **GitHub图床失败**: 检查Token权限(repo)、仓库路径格式、文件大小限制

### 日志查看
页面右侧实时日志面板，详细日志保存在`log.txt`文件中，支持TraceID跟踪

## 📄 许可证

本项目仅供学习和个人使用。请遵守相关API服务的使用条款。

---

**当前版本**: v1.9.3 | **最后更新**: 2025-07-30 | **开发状态**: 活跃开发中